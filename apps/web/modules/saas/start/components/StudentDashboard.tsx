"use client";

import { useSession } from "@saas/auth/hooks/use-session";
import { useViewMode } from "@saas/shared/contexts/ViewModeContext";
import { Card, CardContent } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Skeleton } from "@ui/components/skeleton";
import { BookOpenIcon, GraduationCapIcon, ShieldIcon, EyeIcon, ArrowRightIcon } from "lucide-react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { useUserCourses } from "../hooks/useUserCourses";

interface ContentItem {
	id: string;
	title: string;
	description: string;
	type: "course" | "mentoring" | "video";
	image?: string;
	isAccessible: boolean;
	organizationSlug?: string;
}

interface StudentDashboardProps {
	organizationSlug?: string;
}

export function StudentDashboard({ organizationSlug }: StudentDashboardProps) {
	const { user } = useSession();
	const { toggleViewMode, isStudentView, canToggleViewMode } = useViewMode();
	const router = useRouter();

	const { data: userCoursesResponse, isLoading, error } = useUserCourses(organizationSlug);

	const contentItems = userCoursesResponse?.data || [];
	const accessibleContent = contentItems.filter(item => item.isAccessible);

	if (error) {
		return (
			<div className="space-y-6 sm:space-y-8">
				<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
					<div>
						<h1 className="text-2xl sm:text-3xl font-bold text-foreground">
							Olá, {user?.name} 👋
						</h1>
						<p className="text-muted-foreground mt-2">
							Explore Seus Conteúdos
						</p>
					</div>

					{canToggleViewMode && (
						<div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
							<Badge
								className={`flex items-center justify-center gap-2 px-3 py-2 text-sm ${
									isStudentView
										? "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700"
										: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700"
								}`}
							>
								{isStudentView ? (
									<>
										<GraduationCapIcon className="h-4 w-4" />
										<span className="hidden sm:inline">Visualização do Aluno</span>
										<span className="sm:hidden">Modo Aluno</span>
									</>
								) : (
									<>
										<ShieldIcon className="h-4 w-4" />
										<span className="hidden sm:inline">Visualização do Admin</span>
										<span className="sm:hidden">Modo Admin</span>
									</>
								)}
							</Badge>
							<Button
								variant="outline"
								size="sm"
								onClick={toggleViewMode}
								className="flex items-center justify-center gap-2"
							>
								<EyeIcon className="h-4 w-4" />
								<span className="hidden sm:inline">{isStudentView ? "Ver como Admin" : "Ver como Aluno"}</span>
								<span className="sm:hidden">{isStudentView ? "Admin" : "Aluno"}</span>
							</Button>
						</div>
					)}
				</div>

				<div className="text-center py-12">
					<p className="text-muted-foreground">
						Erro ao carregar seus conteúdos. Tente novamente mais tarde.
					</p>
				</div>
			</div>
		);
	}

	const handleContentClick = (item: ContentItem) => {
		if (!item.isAccessible) {
			return;
		}

		if (item.organizationSlug) {
			router.push(`/app/${item.organizationSlug}/course/${item.id}`);
		}
	};

	if (isLoading) {
		return (
			<div className="space-y-6 sm:space-y-8">
				<div className="space-y-4">
					<Skeleton className="h-8 w-64" />
					<Skeleton className="h-4 w-96" />
				</div>
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
					{[...Array(6)].map((_, i) => (
						<Card key={i} className="overflow-hidden">
							<Skeleton className="h-64 sm:h-96 w-full" />
							<CardContent className="p-4 sm:p-6 space-y-3">
								<Skeleton className="h-6 w-full" />
								<Skeleton className="h-4 w-3/4" />
								<Skeleton className="h-4 w-1/2" />
								<div className="mt-4">
									<Skeleton className="h-10 w-full rounded-md" />
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6 sm:space-y-8">
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h1 className="text-2xl sm:text-3xl font-bold text-foreground">
						Olá, {user?.name} 👋
					</h1>
					<p className="text-muted-foreground mt-2">
						Explore Seus Conteúdos
					</p>
				</div>

				{canToggleViewMode && (
					<div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
						<Badge
							className={`flex items-center justify-center gap-2 px-3 py-2 text-sm ${
								isStudentView
									? "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700"
									: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700"
							}`}
						>
							{isStudentView ? (
								<>
									<GraduationCapIcon className="h-4 w-4" />
									<span className="hidden sm:inline">Visualização do Aluno</span>
									<span className="sm:hidden">Modo Aluno</span>
								</>
							) : (
								<>
									<ShieldIcon className="h-4 w-4" />
									<span className="hidden sm:inline">Visualização do Admin</span>
									<span className="sm:hidden">Modo Admin</span>
								</>
							)}
						</Badge>
						<Button
							variant="outline"
							size="sm"
							onClick={toggleViewMode}
							className="flex items-center justify-center gap-2"
						>
							<EyeIcon className="h-4 w-4" />
							<span className="hidden sm:inline">{isStudentView ? "Ver como Admin" : "Ver como Aluno"}</span>
							<span className="sm:hidden">{isStudentView ? "Admin" : "Aluno"}</span>
						</Button>
					</div>
				)}
			</div>

			{accessibleContent.length > 0 ? (
				<div className="space-y-4 sm:space-y-6">
					<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
						{accessibleContent.map((item, index) => (
							<motion.div
								key={item.id}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ delay: index * 0.1 }}
							>
								<Card
									className="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group"
									onClick={() => handleContentClick(item)}
								>
									<div className="relative h-64 sm:h-96">
										{item.image ? (
											<img
												src={item.image}
												alt={item.title}
												className="w-full h-full object-cover group-hover:opacity-90 transition-opacity duration-300"
											/>
										) : (
											<div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center group-hover:opacity-90 transition-opacity duration-300">
												<BookOpenIcon className="h-16 w-16 text-white opacity-50" />
											</div>
										)}
									</div>

									<CardContent className="p-4 sm:p-6">
										<div className="space-y-3">
											<h3 className="font-semibold text-base sm:text-lg line-clamp-2 group-hover:text-primary transition-colors">
												{item.title}
											</h3>

											<p className="text-muted-foreground text-sm line-clamp-3">
												{item.description}
											</p>

											<div className="flex gap-2 mt-4">
												<Button
													className="flex-1 hover:bg-primary transition-all duration-300 group-hover:translate-x-1 group-hover:bg-primary group-hover:text-primary-foreground"
													variant="outline"
												>
													<span>Acessar</span>
													<ArrowRightIcon className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
												</Button>
											</div>
										</div>
									</CardContent>
								</Card>
							</motion.div>
						))}
					</div>
				</div>
			) : (
				!isLoading && (
					<div className="text-center py-12">
						<BookOpenIcon className="h-16 w-16 text-muted-foreground mx-auto mb-4 opacity-50" />
						<h3 className="text-lg font-medium text-foreground mb-2">
							Nenhum conteúdo disponível
						</h3>
						<p className="text-muted-foreground">
							Você ainda não tem acesso a nenhum curso. Entre em contato com o suporte para mais informações.
						</p>
					</div>
				)
			)}
		</div>
	);
}
