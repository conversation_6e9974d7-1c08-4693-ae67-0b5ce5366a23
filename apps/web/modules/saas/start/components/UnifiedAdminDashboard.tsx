"use client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useOrganizationStats } from "@saas/organizations/hooks/useOrganizationStats";
import { useViewMode } from "@saas/shared/contexts/ViewModeContext";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";
import {
	GraduationCapIcon,
	UsersIcon,
	Building2Icon,
	SettingsIcon,
	ShieldIcon,
	TrendingUpIcon,
	CheckCircleIcon,
	ArrowRightIcon,
	ActivityIcon,
	DatabaseIcon,
	PlusCircleIcon,
	BookOpen,
	EyeIcon,
	CalendarIcon,
	LoaderIcon,
	AlertCircleIcon,
	Users
} from "lucide-react";
import Link from "next/link";
import { Skeleton } from "@ui/components/skeleton";

interface WorkspaceCardProps {
	organization: {
		id: string;
		name: string;
		slug: string;
		logo?: string | null;
		createdAt: string;
		stats: {
			totalMembers: number;
			activeMembers: number;
			totalCourses: number;
			totalEnrollments: number;
			totalVitrines: number;
			publishedVitrines: number;
			totalViews: number;
			daysSinceLastActivity: number | null;
		};
	};
	onSelect: (slug: string) => void;
}

function WorkspaceCard({ organization, onSelect }: WorkspaceCardProps) {
	const getStatusBadge = () => {
		if (organization.stats.totalCourses === 0 && organization.stats.totalVitrines === 0) {
			return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Novo</Badge>;
		}
		if (organization.stats.daysSinceLastActivity && organization.stats.daysSinceLastActivity > 30) {
			return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Inativo</Badge>;
		}
		return <Badge className="bg-green-100 text-green-800 border-green-200">Ativo</Badge>;
	};

	const getLastActivityText = () => {
		if (!organization.stats.daysSinceLastActivity) {
			return "Sem atividade recente";
		}
		if (organization.stats.daysSinceLastActivity === 0) {
			return "Atividade hoje";
		}
		if (organization.stats.daysSinceLastActivity === 1) {
			return "Atividade ontem";
		}
		return `Atividade há ${organization.stats.daysSinceLastActivity} dias`;
	};

	const getEngagementScore = () => {
		if (organization.stats.totalMembers === 0) return 0;
		return Math.round((organization.stats.activeMembers / organization.stats.totalMembers) * 100);
	};

	return (
		<Card className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-background to-muted/20 border-border hover:border-primary/30 overflow-hidden">
			<div className="p-6 pb-6">
				<div className="flex items-start justify-between mb-4">
					<div className="flex items-center gap-3">
						<OrganizationLogo
							name={organization.name}
							logoUrl={organization.logo}
							className="size-12"
						/>
						<div className="min-w-0 flex-1">
							<h3 className="font-semibold text-lg text-foreground truncate">
								{organization.name}
							</h3>
							<div className="flex items-center gap-2 mt-1">
								{getStatusBadge()}
								<span className="text-xs text-muted-foreground">
									Criado em {new Date(organization.createdAt).toLocaleDateString('pt-BR')}
								</span>
							</div>
						</div>
					</div>
				</div>


				<div className="grid grid-cols-2 gap-4 mb-4">
					<div className="text-center p-3 bg-primary/5 rounded-lg">
						<div className="flex items-center justify-center gap-2 mb-1">
							<Users className="h-4 w-4 text-primary" />
							<span className="text-sm font-medium text-muted-foreground">Membros</span>
						</div>
						<div className="text-xl font-bold text-foreground">
							{organization.stats.totalMembers}
						</div>
						{organization.stats.totalMembers > 0 && (
							<div className="text-xs text-muted-foreground">
								{organization.stats.activeMembers} ativos
							</div>
						)}
					</div>

					<div className="text-center p-3 bg-primary/5 rounded-lg">
						<div className="flex items-center justify-center gap-2 mb-1">
							<BookOpen className="h-4 w-4 text-primary" />
							<span className="text-sm font-medium text-muted-foreground">Conteúdo</span>
						</div>
						<div className="text-xl font-bold text-foreground">
							{organization.stats.totalCourses + organization.stats.publishedVitrines}
						</div>
						<div className="text-xs text-muted-foreground">
							{organization.stats.totalCourses} cursos • {organization.stats.publishedVitrines} vitrines
						</div>
					</div>
				</div>


				<div className="flex gap-2 mt-auto">
					<Button
						onClick={() => onSelect(organization.slug)}
						className="flex-1   hover:bg-primary  transition-all duration-300"
						variant="outline"
					>
						<span>Acessar</span>
						<ArrowRightIcon className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
					</Button>
					<Link href={`/app/${organization.slug}/settings/general`} passHref legacyBehavior>
						<Button
							asChild
							variant="outline"
							className="flex items-center gap-2 hover:bg-primary hover:text-primary-foreground"
							title="Configurar Workspace"
						>
							<a>
								<SettingsIcon className="h-4 w-4" />

							</a>
						</Button>
					</Link>
				</div>
			</div>
		</Card>
	);
}

export function UnifiedAdminDashboard() {
	const { user } = useSession();
	const { setActiveOrganization } = useActiveOrganization();
	const { data: organizations, isLoading, error } = useOrganizationStats();
	const { viewMode, toggleViewMode, isStudentView, canToggleViewMode } = useViewMode();

	const quickStats = [
		{
			title: "Total de Usuários",
			value: "935",
			change: "+12%",
			trend: "up",
			icon: UsersIcon,
		},
		{
			title: "Cursos Ativos",
			value: "3",
			change: "+1",
			trend: "up",
			icon: GraduationCapIcon,
		},
		{
			title: "Organizações",
			value: "3",
			change: "0",
			trend: "neutral",
			icon: Building2Icon,
		},
		{
			title: "Taxa de Atividade",
			value: "89%",
			change: "+5%",
			trend: "up",
			icon: ActivityIcon,
		},
	];

	const getColorClasses = (color: string) => {
		switch (color) {
			case "blue":
				return {
					bg: "bg-blue-100 dark:bg-blue-900/30",
					hoverBg: "group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50",
					iconColor: "text-blue-600 dark:text-blue-400",
				};
			case "green":
				return {
					bg: "bg-green-100 dark:bg-green-900/30",
					hoverBg: "group-hover:bg-green-200 dark:group-hover:bg-green-900/50",
					iconColor: "text-green-600 dark:text-green-400",
				};
			case "purple":
				return {
					bg: "bg-purple-100 dark:bg-purple-900/30",
					hoverBg: "group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50",
					iconColor: "text-purple-600 dark:text-purple-400",
				};
			default:
				return {
					bg: "bg-gray-100 dark:bg-gray-900/30",
					hoverBg: "group-hover:bg-gray-200 dark:group-hover:bg-gray-900/50",
					iconColor: "text-gray-600 dark:text-gray-400",
				};
		}
	};

	return (
		<div className="space-y-8">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold text-foreground">
						Olá, {user?.name} 👋
					</h1>
					<p className="text-muted-foreground mt-2">
						Gerencie sua Plataforma de Membros.
					</p>
				</div>

				{canToggleViewMode && (
					<div className="flex items-center gap-3">
						<Badge
							className={`flex items-center gap-2 px-3 py-1 ${
								isStudentView
									? "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700"
									: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700"
							}`}
						>
							{isStudentView ? (
								<>
									<GraduationCapIcon className="h-4 w-4" />
									Visualização do Aluno
								</>
							) : (
								<>
									<ShieldIcon className="h-4 w-4" />
									Visualização do Admin
								</>
							)}
						</Badge>
						<Button
							variant="outline"
							size="sm"
							onClick={toggleViewMode}
							className="flex items-center gap-2"
						>
							<EyeIcon className="h-4 w-4" />
							{isStudentView ? "Ver como Admin" : "Ver como Aluno"}
						</Button>
					</div>
				)}
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				{quickStats.map((stat) => (
					<Card key={stat.title} className="hover:shadow-md transition-shadow">
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										{stat.title}
									</p>
									<div className="flex items-center gap-2 mt-2">
										<p className="text-2xl font-bold text-foreground">
											{stat.value}
										</p>
										{stat.trend === "up" && (
											<Badge status="success" className="text-green-600 flex items-center bg-green-100 dark:bg-green-900/30">
												<TrendingUpIcon className="h-3 w-3 mr-1" />
												{stat.change}
											</Badge>
										)}
									</div>
								</div>
								<div className="p-3 rounded-full bg-primary/10">
									<stat.icon className="h-6 w-6 text-primary" />
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h2 className="text-2xl font-semibold">
							Seus workspaces
						</h2>
						<p className="text-muted-foreground">
							Gerencie e acesse seus workspaces
						</p>
					</div>
					{config.organizations.enableUsersToCreateOrganizations && (
						<Button asChild>
							<Link href="/app/new-organization">
								<PlusCircleIcon className="mr-2 h-4 w-4" />
								Novo Workspace
							</Link>
						</Button>
					)}
				</div>

				{isLoading && (
					<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
						{Array.from({ length: 3 }).map((_, index) => (
							<Card key={index} className="group hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-background to-muted/20 border-border overflow-hidden animate-pulse">
								<div className="p-6 pb-6">
									<div className="flex items-start justify-between mb-4">
										<div className="flex items-center gap-3">
											<Skeleton className="size-12 rounded-lg" />
											<div className="min-w-0 flex-1">
												<Skeleton className="h-6 w-32 mb-2" />
												<div className="flex items-center gap-2">
													<Skeleton className="h-5 w-16" />
													<Skeleton className="h-3 w-24" />
												</div>
											</div>
										</div>
									</div>

									<div className="grid grid-cols-2 gap-4 mb-4">
										<div className="text-center p-3 bg-primary/5 rounded-lg">
											<div className="flex items-center justify-center gap-2 mb-1">
												<Skeleton className="h-4 w-4 rounded" />
												<Skeleton className="h-3 w-16" />
											</div>
											<Skeleton className="h-6 w-8 mx-auto mb-1" />
											<Skeleton className="h-3 w-12 mx-auto" />
										</div>

										<div className="text-center p-3 bg-primary/5 rounded-lg">
											<div className="flex items-center justify-center gap-2 mb-1">
												<Skeleton className="h-4 w-4 rounded" />
												<Skeleton className="h-3 w-16" />
											</div>
											<Skeleton className="h-6 w-8 mx-auto mb-1" />
											<Skeleton className="h-3 w-20 mx-auto" />
										</div>
									</div>

									<div className="flex gap-2">
										<Skeleton className="h-10 flex-1 rounded-md" />
										<Skeleton className="h-10 w-10 rounded-md" />
									</div>
								</div>
							</Card>
						))}

						{config.organizations.enableUsersToCreateOrganizations && (
							<Card className="group hover:shadow-lg transition-all duration-300 border-dashed border-2 border-muted-foreground/30 overflow-hidden animate-pulse">
								<div className="flex flex-col items-center justify-center h-full min-h-[270px] text-center space-y-4 p-6">
									<Skeleton className="p-4 bg-primary/10 rounded-full" />
									<div className="space-y-2">
										<Skeleton className="h-6 w-40 mx-auto" />
										<Skeleton className="h-4 w-48 mx-auto" />
									</div>
								</div>
							</Card>
						)}
					</div>
				)}

				{error && (
					<Card className="p-12">
						<div className="flex flex-col items-center justify-center text-center">
							<AlertCircleIcon className="h-12 w-12 text-red-500 mb-4" />
							<h3 className="text-lg font-semibold mb-2">Erro ao carregar workspaces</h3>
							<p className="text-muted-foreground mb-4">
								{error instanceof Error ? error.message : "Erro interno do servidor"}
							</p>
							<Button onClick={() => window.location.reload()} variant="outline">
								Tentar novamente
							</Button>
						</div>
					</Card>
				)}

				{!isLoading && !error && (
					<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
						{organizations?.filter((org: any) => org.slug).map((organization: any) => (
							<WorkspaceCard
								key={organization.id}
								organization={{
									...organization,
									slug: organization.slug || "",
								}}
								onSelect={(slug: string) => setActiveOrganization(slug)}
							/>
						))}

						{config.organizations.enableUsersToCreateOrganizations && (
							<Card className="group hover:shadow-lg transition-all duration-300 border-dashed border-2 border-muted-foreground/30 hover:border-primary/50 overflow-hidden">
								<Link
									href="/app/new-organization"
									className="flex flex-col items-center justify-center h-full min-h-[270px] text-center space-y-4 text-muted-foreground hover:text-primary transition-colors p-6"
								>
									<div className="p-4 bg-primary/10 rounded-full group-hover:bg-primary/20 transition-colors">
										<PlusCircleIcon className="h-8 w-8" />
									</div>
									<div>
										<h3 className="font-semibold text-lg">
											Criar novo workspace
										</h3>
										<p className="text-sm mt-2 text-muted-foreground">
											Crie uma nova área de membros para compartilhar conhecimento
										</p>
									</div>
								</Link>
							</Card>
						)}
					</div>
				)}
			</div>
		</div>
	);
}
