import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { CoursePreviewData } from '../types/course-preview';

export function useCoursePreview(courseId: string, organizationSlug: string) {
  const [isEnabled, setIsEnabled] = useState(false);

  const {
    data: courseData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['course-preview', courseId, organizationSlug],
    queryFn: async (): Promise<CoursePreviewData> => {
      // Aqui você faria a chamada real para a API
      // Por enquanto, retornando dados mock
      const response = await fetch(`/api/courses/${courseId}/preview?organizationSlug=${organizationSlug}`);

      if (!response.ok) {
        throw new Error('Failed to fetch course preview');
      }

      return response.json();
    },
    enabled: isEnabled && !!courseId && !!organizationSlug,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  const fetchCoursePreview = () => {
    setIsEnabled(true);
    refetch();
  };

  return {
    courseData,
    isLoading,
    error,
    fetchCoursePreview,
    reset: () => setIsEnabled(false)
  };
}

// Hook para buscar dados mock (temporário)
export function useMockCoursePreview(courseId: string) {
  const getMockData = (): CoursePreviewData => {
    return {
      id: courseId,
      title: "Intensivo Microprodutos",
      description: "Aprenda a criar e vender microprodutos de forma eficiente",
      logo: "/images/cards/card1.jpg",
      modules: [
        {
          id: "module-1",
          title: "Microprodutos",
          lessons: [
            {
              id: "lesson-1",
              title: "Intensivo Microprodutos",
              duration: "28 min",
              isCompleted: false,
              videoUrl: "https://example.com/video1.mp4"
            }
          ]
        }
      ],
      firstLesson: {
        id: "lesson-1",
        title: "Intensivo Microprodutos",
        duration: "28 min",
        videoUrl: "https://example.com/video1.mp4"
      }
    };
  };

  return {
    courseData: getMockData(),
    isLoading: false,
    error: null,
    fetchCoursePreview: () => {},
    reset: () => {}
  };
}

// Hook para buscar dados reais do curso
export function useRealCoursePreview(courseId: string, organizationSlug: string) {
  const [isEnabled, setIsEnabled] = useState(false);

  const {
    data: courseData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['course-preview', courseId, organizationSlug],
    queryFn: async (): Promise<CoursePreviewData> => {
      const response = await fetch(`/api/courses/${courseId}/preview?organizationSlug=${organizationSlug}`);

      if (!response.ok) {
        throw new Error('Failed to fetch course preview');
      }

      return response.json();
    },
    enabled: isEnabled && !!courseId && !!organizationSlug,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
  });

  const fetchCoursePreview = useCallback(() => {
    setIsEnabled(true);
    refetch();
  }, [refetch]);

  const reset = useCallback(() => {
    setIsEnabled(false);
  }, []);

  return {
    courseData,
    isLoading,
    error,
    fetchCoursePreview,
    reset
  };
}
