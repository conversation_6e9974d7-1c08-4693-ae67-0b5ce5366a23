import React from 'react';
import { render, screen } from '@testing-library/react';
import { CoursePreviewModal } from './CoursePreviewModal';

// Mock data para teste
const mockCourse = {
  id: "test-course",
  title: "Test Course",
  description: "Test Description",
  modules: [
    {
      id: "module-1",
      title: "Module 1",
      lessons: [
        {
          id: "lesson-1",
          title: "Lesson 1",
          duration: "10 min",
          isCompleted: false,
          videoUrl: "https://example.com/video1.mp4"
        }
      ]
    }
  ],
  firstLesson: {
    id: "lesson-1",
    title: "Lesson 1",
    duration: "10 min",
    videoUrl: "https://example.com/video1.mp4"
  }
};

describe('CoursePreviewModal', () => {
  it('should render when open', () => {
    render(
      <CoursePreviewModal
        isOpen={true}
        onClose={() => {}}
        course={mockCourse}
        onPlayLesson={() => {}}
      />
    );

    expect(screen.getByText('Test Course')).toBeInTheDocument();
    expect(screen.getByText('Module 1')).toBeInTheDocument();
    expect(screen.getByText('Lesson 1')).toBeInTheDocument();
  });

  it('should not render when closed', () => {
    render(
      <CoursePreviewModal
        isOpen={false}
        onClose={() => {}}
        course={mockCourse}
        onPlayLesson={() => {}}
      />
    );

    expect(screen.queryByText('Test Course')).not.toBeInTheDocument();
  });
});
