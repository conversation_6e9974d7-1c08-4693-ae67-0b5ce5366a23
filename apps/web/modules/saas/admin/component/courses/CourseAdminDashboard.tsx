'use client'

import { useState, useMemo, useCallback } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent } from '@/modules/ui/components/card'
import { Button } from '@/modules/ui/components/button'
import { Badge } from '@/modules/ui/components/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/modules/ui/components/avatar'
import {
  PlusIcon,
  GraduationCapIcon,
  UsersIcon,
  BookOpenIcon,
  EyeIcon,
  EditIcon,
  TrashIcon,
  ImageIcon,
  AlertTriangleIcon,
  LoaderIcon,
  AlertCircleIcon,
  PackageIcon
} from 'lucide-react'
import { toast } from 'sonner'
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/modules/ui/components/select'
import { AdminPageLayout } from '../shared/AdminPageLayout'
import { useAdminCourses } from '../../hooks/useAdminCourses'
import { CaktoProductSelector } from './CaktoProductSelector'
import { CourseGridSkeleton } from './CourseCardSkeleton'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/modules/ui/components/alert-dialog'

export function CourseAdminDashboard() {
  const router = useRouter()
  const [selectedOrg, setSelectedOrg] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [courseToDelete, setCourseToDelete] = useState<any>(null)
  const [productSelectorOpen, setProductSelectorOpen] = useState(false)

  // Fetch courses using the real API
  const {
    courses,
    organizations,
    isLoading,
    error,
    isDeleting,
    deleteCourse: deleteCourseApi,
    refetch
  } = useAdminCourses({
    organizationSlug: selectedOrg === 'all' ? undefined : selectedOrg,
    status: statusFilter === 'all' ? undefined : statusFilter.toUpperCase() as any,
  })

  const filteredCourses = courses

  const handleCreateCourse = useCallback(() => {
    setProductSelectorOpen(true)
  }, [])

  const handleViewCourse = useCallback((course: any) => {
    router.push(`/app/${course.organization.slug}/course/${course.id}`)
  }, [router])

  const handleEditCourse = useCallback((course: any) => {
    router.push(`/app/admin/courses/${course.id}/edit`)
  }, [router])

  const handleDeleteCourse = useCallback((course: any) => {
    setCourseToDelete(course)
    setDeleteConfirmOpen(true)
  }, [])

  const confirmDeleteCourse = useCallback(async () => {
    if (courseToDelete) {
      const success = await deleteCourseApi(courseToDelete.id)
      if (success) {
        toast.success(`Curso "${courseToDelete.name}" removido com sucesso`)
        setDeleteConfirmOpen(false)
        setCourseToDelete(null)
      } else {
        toast.error("Erro ao remover o curso. Tente novamente.")
      }
    }
  }, [courseToDelete, deleteCourseApi])

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'published': return 'success'
      case 'draft': return 'warning'
      case 'archived': return 'muted'
      default: return 'muted'
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'published': return 'Publicado'
      case 'draft': return 'Em rascunho'
      case 'archived': return 'Arquivado'
      default: return status
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'free': return 'Curso Gratuito'
      case 'paid': return 'Curso Pago'
      default: return type
    }
  }

  return (
    <>
      <AdminPageLayout
        title="Gestão de Cursos"
        subtitle="Gerencie todos os cursos das organizações"
        actionButton={{
          label: "Novo Curso",
          onClick: handleCreateCourse,
          icon: <PackageIcon className="mr-2 h-4 w-4" />,
        }}
      >
        {/* Filtros */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <Select value={selectedOrg} onValueChange={setSelectedOrg}>
            <SelectTrigger className="w-full sm:w-48 min-w-0">
              <SelectValue placeholder="Organização" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Workspaces</SelectItem>
              {organizations.map((org: any) => (
                <SelectItem key={org.slug} value={org.slug}>
                  {org.name} ({org.coursesCount} cursos)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-48 min-w-0">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Status</SelectItem>
              <SelectItem value="published">Publicado</SelectItem>
              <SelectItem value="draft">Rascunho</SelectItem>
              <SelectItem value="archived">Arquivado</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Loading state */}
        {isLoading ? (
          <CourseGridSkeleton count={6} />
        ) : error ? (
          /* Error state */
          <Card>
            <CardContent className="py-12">
              <div className="flex flex-col items-center justify-center">
                <AlertCircleIcon className="h-12 w-12 text-red-500 mb-4" />
                <h3 className="text-lg font-semibold mb-2">Erro ao carregar cursos</h3>
                <p className="text-muted-foreground text-center mb-4">{error}</p>
                <Button onClick={refetch} variant="outline">
                  Tentar novamente
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : filteredCourses.length === 0 ? (
          /* Empty state */
          <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <GraduationCapIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum curso encontrado</h3>
                <p className="text-gray-600 mb-4">
                  {selectedOrg !== 'all' || statusFilter !== 'all'
                    ? 'Tente ajustar os filtros de busca'
                    : 'Comece criando seu primeiro curso'
                  }
                </p>
                <Button onClick={handleCreateCourse}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Criar Primeiro Curso
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
            {filteredCourses.map(course => (
              <Card key={course.id} className="overflow-hidden hover:shadow-lg transition-all duration-200 group h-full">
                                <CardContent className="p-0">
                  <div className="flex h-full">
                    {/* Course Image - Left Side */}
                    <div className="relative w-32 lg:w-56 flex-shrink-0">
                      {course.logo ? (
                        <img
                          src={course.logo}
                          alt={course.name}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200 rounded-l-lg"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center rounded-l-lg">
                          <ImageIcon className="h-8 w-8 text-primary/40" />
                        </div>
                      )}

                      {/* Status Badge - Top Right of Content */}

                    </div>

                    {/* Course Content - Right Side */}
                    <div className="flex-1 p-4 flex flex-col justify-between min-w-0">
                      <div className="space-y-2">
                        {/* Header with Course Type and Status Badge */}
                        <div className="flex justify-between items-start">
                          <p className="text-xs text-muted-foreground">
                            {getTypeLabel(course.type)}
                          </p>
                          <Badge status={getStatusVariant(course.status)}>
                            {getStatusLabel(course.status)}
                          </Badge>
                        </div>

                        {/* Course Title */}
                        <div>
                          <h3 className="font-semibold text-base text-foreground mb-1 line-clamp-2">
                            {course.name}
                          </h3>

                        </div>

                        {/* Organization */}
                        <div className="text-xs text-muted-foreground truncate">
                          {course.organization.name}
                        </div>

                        {/* Stats */}
                        <div className="flex items-center gap-3 text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <UsersIcon className="h-3 w-3" />
                            {course.studentsCount} alunos
                          </span>
                          <span className="flex items-center gap-1">
                            <BookOpenIcon className="h-3 w-3" />
                            {course.modulesCount} módulos
                          </span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center justify-end pt-2 border-t mt-3 border-border">
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewCourse(course)}
                            className="h-7 px-2"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            Ver
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditCourse(course)}
                            className="h-7 px-2"
                          >
                            <EditIcon className="h-3 w-3 mr-1" />
                            Editar
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteCourse(course)}
                            disabled={isDeleting === course.id}
                            className="text-destructive hover:text-destructive hover:bg-destructive/10 h-7 px-2"
                          >
                            {isDeleting === course.id ? (
                              <LoaderIcon className="h-3 w-3 animate-spin" />
                            ) : (
                              <TrashIcon className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </AdminPageLayout>



      {/* Modal de confirmação de exclusão */}
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangleIcon className="h-5 w-5 text-destructive" />
              Confirmar exclusão
            </AlertDialogTitle>
            <AlertDialogDescription>
              {courseToDelete && (
                <>
                  Tem certeza que deseja excluir o curso <strong>{courseToDelete.name}</strong>?
                  <br />
                  Esta ação não pode ser desfeita.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteCourse}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Modal de seleção de produtos da Cakto */}
      <CaktoProductSelector
        open={productSelectorOpen}
        onOpenChange={setProductSelectorOpen}
      />
    </>
  )
}
