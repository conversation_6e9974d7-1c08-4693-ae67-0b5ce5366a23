'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Button } from '@/modules/ui/components/button'
import { Badge } from '@/modules/ui/components/badge'
import { Input } from '@/modules/ui/components/input'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/modules/ui/components/sheet'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/modules/ui/components/select'
import {
  LoaderIcon,
  SearchIcon,
  PackageIcon,
  CheckIcon,
  AlertCircleIcon,
  ExternalLinkIcon,
  TagIcon,
  ZapIcon,
  PlusIcon,
  FilterIcon,
  ImageIcon,
  BookOpenIcon,
  GraduationCapIcon,
  UsersIcon,
  StarIcon
} from 'lucide-react'
import { toast } from 'sonner'
import { useCaktoProducts } from '../../hooks/useCaktoProducts'

interface CaktoProduct {
  id: string
  name: string
  description?: string
  image?: string
  price: number
  type: 'course' | 'ebook' | 'mentorship' | string
  status: 'active' | 'inactive' | string
  organizationId?: string
  contentDeliveries: string[]
  alreadyLinked: boolean
}

interface CaktoProductSelectorProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProductSelect?: (product: CaktoProduct) => void
}

// Helper function to format price
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(price)
}

const getProductTypeLabel = (type: string) => {
  switch (type) {
    case 'course': return 'Curso'
    case 'ebook': return 'E-book'
    case 'mentorship': return 'Mentoria'
    case 'unique': return 'Produto Único'
    default: return type
  }
}

export function CaktoProductSelector({ open, onOpenChange, onProductSelect }: CaktoProductSelectorProps) {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProduct, setSelectedProduct] = useState<CaktoProduct | null>(null)
  const [filterType, setFilterType] = useState<string>('all')
  const [showOnlyAvailable, setShowOnlyAvailable] = useState(true)

  const { products, isLoading, error } = useCaktoProducts({ enabled: open })

  const filteredProducts = products.filter((product: CaktoProduct) => {
    // Search filter
    const matchesSearch = searchTerm === '' ||
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.description?.toLowerCase() || '').includes(searchTerm.toLowerCase())

    // Type filter
    const matchesType = filterType === 'all' || product.type === filterType

    // Availability filter
    const matchesAvailability = !showOnlyAvailable || !product.alreadyLinked

    return matchesSearch && matchesType && matchesAvailability
  })

  const handleProductSelect = (product: CaktoProduct) => {
    if (product.alreadyLinked) {
      toast.error('Este produto já está associado a outro curso')
      return
    }
    setSelectedProduct(product)
  }

  const handleKeyDown = (e: React.KeyboardEvent, product: CaktoProduct) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleProductSelect(product)
    }
  }

  const handleConfirmSelection = () => {
    if (!selectedProduct) {
      toast.error('Selecione um produto')
      return
    }

    if (onProductSelect) {
      // Callback para seleção direta
      onProductSelect(selectedProduct)
      onOpenChange(false)
      setSelectedProduct(null)
      setSearchTerm('')
    } else {
      // Navegar para página de criar produto
      router.push(`/app/admin/courses/create?caktoProductId=${selectedProduct.id}&caktoProductName=${encodeURIComponent(selectedProduct.name)}`)
    }
  }

  const handleCreateWithProduct = () => {
    if (!selectedProduct) {
      toast.error('Selecione um produto')
      return
    }

    // Navegar para página de criar produto
    router.push(`/app/admin/courses/create?caktoProductId=${selectedProduct.id}&caktoProductName=${encodeURIComponent(selectedProduct.name)}`)
  }

  const handleCancel = () => {
    setSelectedProduct(null)
    setSearchTerm('')
    onOpenChange(false)
  }

  // Don't render anything if sheet is not open
  if (!open) {
    return null
  }

  if (error) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent className="sm:max-w-[600px]">
          <SheetHeader>
            <SheetTitle>Erro ao carregar produtos</SheetTitle>
            <SheetDescription>
              Não foi possível carregar os produtos da Cakto. Tente novamente.
            </SheetDescription>
          </SheetHeader>
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <AlertCircleIcon className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <p className="text-sm text-gray-600">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4"
                variant="outline"
              >
                Tentar novamente
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    )
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:w-[700px] lg:w-[800px] max-w-[95vw] flex flex-col">
        <SheetHeader className="pb-6 flex-shrink-0 border-b">
          <div className="flex items-center justify-between">
            <div>
              <SheetTitle className="flex items-center gap-3 text-xl font-semibold">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <PackageIcon className="h-5 w-5 text-primary" />
                </div>
                Selecionar Produto da Cakto
              </SheetTitle>
              <SheetDescription className="text-sm text-muted-foreground mt-2">
                Escolha um produto da Cakto para criar seu curso. Apenas produtos disponíveis são exibidos.
              </SheetDescription>
            </div>
            {selectedProduct && (
              <div className="text-right">
                <div className="text-xs text-muted-foreground">Selecionado</div>
                <div className="text-sm font-medium text-primary">{selectedProduct.name}</div>
              </div>
            )}
          </div>

          {/* Search and Filters */}
          <div className="space-y-4 mt-6">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar produtos por nome ou descrição..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-10 bg-background"
              />
            </div>

            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
              <div className="flex items-center gap-2">
                <FilterIcon className="h-4 w-4 text-muted-foreground" />
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-full sm:w-32 h-9">
                    <SelectValue placeholder="Tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos</SelectItem>
                    <SelectItem value="course">Curso</SelectItem>
                    <SelectItem value="ebook">E-book</SelectItem>
                    <SelectItem value="mentorship">Mentoria</SelectItem>
                    <SelectItem value="unique">Único</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="showOnlyAvailable"
                  checked={showOnlyAvailable}
                  onChange={(e) => setShowOnlyAvailable(e.target.checked)}
                  className="rounded border-input"
                />
                <label htmlFor="showOnlyAvailable" className="text-sm text-muted-foreground">
                  Apenas disponíveis
                </label>
              </div>
            </div>
          </div>
        </SheetHeader>

        <div className="flex-1 flex flex-col min-h-0">
          {/* Results Summary */}
          <div className="flex items-center justify-between py-4 flex-shrink-0">
            <div className="text-sm text-muted-foreground">
              {isLoading ? (
                "Carregando produtos..."
              ) : (
                `${filteredProducts.length} produto${filteredProducts.length !== 1 ? 's' : ''} encontrado${filteredProducts.length !== 1 ? 's' : ''}`
              )}
            </div>
            {(searchTerm || filterType !== 'all' || !showOnlyAvailable) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchTerm('')
                  setFilterType('all')
                  setShowOnlyAvailable(true)
                }}
                className="text-xs h-7"
              >
                Limpar filtros
              </Button>
            )}
          </div>

          {/* Products List */}
          <div className="flex-1 overflow-y-auto min-h-0">
            {isLoading ? (
              <div className="flex items-center justify-center p-12">
                <div className="text-center">
                  <LoaderIcon className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                  <p className="text-sm text-muted-foreground">Carregando produtos da Cakto...</p>
                </div>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center p-12">
                <div className="p-4 bg-muted/30 rounded-full w-fit mx-auto mb-6">
                  <PackageIcon className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">
                  {searchTerm || filterType !== 'all' || !showOnlyAvailable
                    ? 'Nenhum produto encontrado'
                    : 'Nenhum produto disponível'
                  }
                </h3>
                <p className="text-sm text-muted-foreground mb-6 max-w-md mx-auto">
                  {searchTerm || filterType !== 'all' || !showOnlyAvailable
                    ? 'Tente ajustar os filtros ou termos de busca para encontrar produtos disponíveis.'
                    : 'Não há produtos da Cakto disponíveis no momento. Verifique sua conta Cakto ou entre em contato com o suporte.'
                  }
                </p>
                {searchTerm && (
                  <Button
                    variant="outline"
                    onClick={() => setSearchTerm('')}
                    className="mb-2"
                  >
                    Limpar busca
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-3 pb-4">
                {filteredProducts.map((product: CaktoProduct) => {
                  const isSelected = selectedProduct?.id === product.id
                  const getProductIcon = () => {
                    switch (product.type) {
                      case 'course': return GraduationCapIcon
                      case 'ebook': return BookOpenIcon
                      case 'mentorship': return UsersIcon
                      default: return PackageIcon
                    }
                  }
                  const ProductIcon = getProductIcon()

                  return (
                    <Card
                      key={product.id}
                      className={`cursor-pointer transition-all duration-200 hover:shadow-md group focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2 ${
                        isSelected
                          ? 'ring-2 ring-primary border-primary bg-primary/5'
                          : 'hover:border-primary/30 border-border'
                      } ${product.alreadyLinked ? 'opacity-60' : ''}`}
                      onClick={() => handleProductSelect(product)}
                      onKeyDown={(e) => handleKeyDown(e, product)}
                      tabIndex={0}
                      role="button"
                      aria-pressed={isSelected}
                      aria-label={`Selecionar produto ${product.name} - ${formatPrice(product.price)}`}
                    >
                      <CardContent className="p-4 sm:p-6">
                        <div className="flex gap-3 sm:gap-4">
                          {/* Product Image/Icon */}
                          <div className="flex-shrink-0">
                            {product.image ? (
                              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-lg overflow-hidden bg-muted">
                                <img
                                  src={product.image}
                                  alt={product.name}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                            ) : (
                              <div className={`w-12 h-12 sm:w-16 sm:h-16 rounded-lg flex items-center justify-center ${
                                isSelected ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
                              }`}>
                                <ProductIcon className="h-6 w-6 sm:h-8 sm:w-8" />
                              </div>
                            )}
                          </div>

                          {/* Product Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1 min-w-0">
                                <h3 className="font-semibold text-base sm:text-lg text-foreground mb-1 line-clamp-1">
                                  {product.name}
                                </h3>
                                <p className="text-sm text-muted-foreground line-clamp-2 mb-2 sm:mb-3">
                                  {product.description || "Sem descrição disponível"}
                                </p>
                              </div>

                              {/* Selection Indicator */}
                              {isSelected && (
                                <div className="flex-shrink-0 ml-2 sm:ml-3">
                                  <div className="bg-primary text-primary-foreground rounded-full p-1.5">
                                    <CheckIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                                  </div>
                                </div>
                              )}
                            </div>

                            {/* Product Details */}
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                                <div className="text-lg sm:text-xl font-bold text-success">
                                  {formatPrice(product.price)}
                                </div>
                                <div className="flex items-center gap-2 flex-wrap">
                                  <Badge
                                    status={product.status === 'active' ? 'success' : 'muted'}
                                    className="text-xs"
                                  >
                                    {product.status === 'active' ? 'Ativo' : 'Inativo'}
                                  </Badge>
                                  <Badge status="info" className="text-xs">
                                    {getProductTypeLabel(product.type)}
                                  </Badge>
                                </div>
                              </div>

                              {product.alreadyLinked && (
                                <Badge status="warning" className="text-xs">
                                  Já vinculado
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            )}
          </div>
        </div>

        {/* Action Footer */}
        <div className="flex-shrink-0 pt-4 sm:pt-6 border-t bg-background">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="text-sm text-muted-foreground">
              {selectedProduct ? (
                <div className="flex items-center gap-2">
                  <CheckIcon className="h-4 w-4 text-success" />
                  <span className="line-clamp-1">
                    <span className="font-medium">{selectedProduct.name}</span> selecionado
                  </span>
                </div>
              ) : (
                `${filteredProducts.length} produto${filteredProducts.length !== 1 ? 's' : ''} disponível${filteredProducts.length !== 1 ? 'eis' : ''}`
              )}
            </div>

            <div className="flex gap-3 flex-shrink-0">
              <Button
                variant="outline"
                onClick={handleCancel}
                className="px-4 sm:px-6"
              >
                Cancelar
              </Button>
              {onProductSelect ? (
                <Button
                  onClick={handleConfirmSelection}
                  disabled={!selectedProduct}
                  className="px-4 sm:px-6"
                >
                  <CheckIcon className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Selecionar Produto</span>
                  <span className="sm:hidden">Selecionar</span>
                </Button>
              ) : (
                <Button
                  onClick={handleCreateWithProduct}
                  disabled={!selectedProduct}
                  className="px-4 sm:px-6"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Criar Curso</span>
                  <span className="sm:hidden">Criar</span>
                </Button>
              )}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}
