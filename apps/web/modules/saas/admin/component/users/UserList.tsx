"use client";

import { authClient } from "@repo/auth/client";
import { adminUsersQuery<PERSON><PERSON>, useAdminUsersQuery } from "@saas/admin/lib/api";
import { useConfirmationAlert } from "@saas/shared/components/ConfirmationAlertProvider";
import { Pagination } from "@saas/shared/components/Pagination";
import { Spinner } from "@shared/components/Spinner";
import { UserAvatar } from "@shared/components/UserAvatar";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Card, CardHeader, CardTitle, CardContent } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Input } from "@ui/components/input";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	MoreVerticalIcon,
	Repeat1Icon,
	ShieldCheckIcon,
	ShieldXIcon,
	SquareUserRoundIcon,
	TrashIcon,
	PlusIcon,
	Search,
	Filter,
	ChevronLeft,
	ChevronRight,
	ChevronsLeft,
	ChevronsRight,
	UsersIcon,
	ShieldIcon,
	MailIcon,
	ClockIcon,
	Building2Icon,
	User2
} from "lucide-react";
import { useTranslations } from "next-intl";
import { parseAsInteger, parseAsString, useQueryState } from "nuqs";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { useDebounceValue } from "usehooks-ts";
import { EmailVerified } from "../EmailVerified";
import { CreateUserModal } from "./CreateUserModal";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { UserRoleIcon } from "@/modules/saas/admin/component/UserRoleIcon";

const ITEMS_PER_PAGE = 12;

interface UsersPaginationProps {
	pagination: {
		total: number;
		pages: number;
		limit: number;
	};
	currentPage: number;
	onPageChange: (page: number) => void;
}

function UsersPaginationComponent({
	pagination,
	currentPage,
	onPageChange
}: UsersPaginationProps) {
	const { total, pages, limit } = pagination;

	const startItem = (currentPage - 1) * limit + 1;
	const endItem = Math.min(currentPage * limit, total);

	const canGoPrevious = currentPage > 1;
	const canGoNext = currentPage < pages;

	const getVisiblePages = () => {
		const delta = 2;
		const range = [];
		const rangeWithDots = [];

		for (
			let i = Math.max(2, currentPage - delta);
			i <= Math.min(pages - 1, currentPage + delta);
			i++
		) {
			range.push(i);
		}

		if (currentPage - delta > 2) {
			rangeWithDots.push(1, '...');
		} else {
			rangeWithDots.push(1);
		}

		rangeWithDots.push(...range);

		if (currentPage + delta < pages - 1) {
			rangeWithDots.push('...', pages);
		} else {
			if (pages > 1) {
				rangeWithDots.push(pages);
			}
		}

		return rangeWithDots;
	};

	if (pages <= 1) {
		return null;
	}

	return (
		<div className="flex items-center justify-between px-2">
			<div className="flex-1 text-sm text-muted-foreground">
				Mostrando {startItem} a {endItem} de {total} membros
			</div>

			<div className="flex items-center space-x-2">
				{/* First page */}
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(1)}
					disabled={!canGoPrevious}
					className="hidden sm:flex"
				>
					<ChevronsLeft className="h-4 w-4" />
				</Button>

				{/* Previous page */}
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(currentPage - 1)}
					disabled={!canGoPrevious}
				>
					<ChevronLeft className="h-4 w-4" />
					<span className="hidden sm:inline ml-1">Anterior</span>
				</Button>

				{/* Page numbers */}
				<div className="hidden md:flex items-center space-x-1">
					{getVisiblePages().map((page, index) => (
						<Button
							key={index}
							variant={page === currentPage ? "primary" : "outline"}
							size="sm"
							onClick={() => typeof page === 'number' && onPageChange(page)}
							disabled={page === '...'}
							className="min-w-[2.5rem]"
						>
							{page}
						</Button>
					))}
				</div>

				{/* Current page indicator for mobile */}
				<div className="md:hidden text-sm text-muted-foreground">
					{currentPage} / {pages}
				</div>

				{/* Next page */}
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(currentPage + 1)}
					disabled={!canGoNext}
				>
					<span className="hidden sm:inline mr-1">Próxima</span>
					<ChevronRight className="h-4 w-4" />
				</Button>

				{/* Last page */}
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(pages)}
					disabled={!canGoNext}
					className="hidden sm:flex"
				>
					<ChevronsRight className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);
}

export function UserList() {
	const t = useTranslations();
	const queryClient = useQueryClient();
	const { confirm } = useConfirmationAlert();
	const { activeOrganization } = useActiveOrganization();
	const [showCreateUserModal, setShowCreateUserModal] = useState(false);
	const [currentPage, setCurrentPage] = useQueryState(
		"currentPage",
		parseAsInteger.withDefault(1),
	);
	const [searchTerm, setSearchTerm] = useQueryState(
		"query",
		parseAsString.withDefault(""),
	);
	const [roleFilter, setRoleFilter] = useQueryState(
		"role",
		parseAsString.withDefault("all"),
	);
	const [emailVerifiedFilter, setEmailVerifiedFilter] = useQueryState(
		"emailVerified",
		parseAsString.withDefault("all"),
	);
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useDebounceValue(
		searchTerm,
		300,
		{
			leading: true,
			trailing: false,
		},
	);

	useEffect(() => {
		setDebouncedSearchTerm(searchTerm);
	}, [searchTerm]);

	const { data, isLoading, refetch } = useAdminUsersQuery({
		itemsPerPage: ITEMS_PER_PAGE,
		currentPage,
		searchTerm: debouncedSearchTerm,
	});

	useEffect(() => {
		setCurrentPage(1);
	}, [debouncedSearchTerm, roleFilter, emailVerifiedFilter]);

	const impersonateUser = async (
		userId: string,
		{ name }: { name: string },
	) => {
		const toastId = toast.loading(
			t("admin.users.impersonation.impersonating", {
				name,
			}),
		);

		await authClient.admin.impersonateUser({
			userId,
		});
		await refetch();
		toast.dismiss(toastId);
		window.location.href = new URL(
			"/app",
			window.location.origin,
		).toString();
	};

	const deleteUser = async (id: string) => {
		toast.promise(
			async () => {
				const { error } = await authClient.admin.removeUser({
					userId: id,
				});

				if (error) {
					throw error;
				}
			},
			{
				loading: t("admin.users.deleteUser.deleting"),
				success: () => {
					return t("admin.users.deleteUser.deleted");
				},
				error: t("admin.users.deleteUser.notDeleted"),
			},
		);
	};

	const resendVerificationMail = async (email: string) => {
		toast.promise(
			async () => {
				const { error } = await authClient.sendVerificationEmail({
					email,
				});

				if (error) {
					throw error;
				}
			},
			{
				loading: t("admin.users.resendVerificationMail.submitting"),
				success: () => {
					return t("admin.users.resendVerificationMail.success");
				},
				error: t("admin.users.resendVerificationMail.error"),
			},
		);
	};

	const assignAdminRole = async (id: string) => {
		await authClient.admin.setRole({
			userId: id,
			role: "admin",
		});

		await queryClient.invalidateQueries({
			queryKey: adminUsersQueryKey,
		});
	};

	const removeAdminRole = async (id: string) => {
		await authClient.admin.setRole({
			userId: id,
			role: "user",
		});

		await queryClient.invalidateQueries({
			queryKey: adminUsersQueryKey,
		});
	};

	const handleCreateUser = () => {
		setShowCreateUserModal(true);
	};

	const handleUserCreated = () => {
		refetch();
	};

	const users = useMemo(() => {
		let filteredUsers = data?.users ?? [];

		// Filter by role
		if (roleFilter !== "all") {
			filteredUsers = filteredUsers.filter(user =>
				roleFilter === "admin" ? user.role === "admin" : user.role !== "admin"
			);
		}

		// Filter by email verification
		if (emailVerifiedFilter !== "all") {
			filteredUsers = filteredUsers.filter(user =>
				emailVerifiedFilter === "verified" ? user.emailVerified : !user.emailVerified
			);
		}

		return filteredUsers;
	}, [data?.users, roleFilter, emailVerifiedFilter]);

	const pagination = useMemo(() => {
		const total = users.length;
		const pages = Math.ceil(total / ITEMS_PER_PAGE);
		return { total, pages, limit: ITEMS_PER_PAGE };
	}, [users.length]);

	const paginatedUsers = useMemo(() => {
		const start = (currentPage - 1) * ITEMS_PER_PAGE;
		const end = start + ITEMS_PER_PAGE;
		return users.slice(start, end);
	}, [users, currentPage]);

	const clearFilters = () => {
		setSearchTerm("");
		setRoleFilter("all");
		setEmailVerifiedFilter("all");
		setCurrentPage(1);
	};

	const hasActiveFilters = searchTerm || roleFilter !== "all" || emailVerifiedFilter !== "all";

	// Get workspace context
	const workspaceContext = activeOrganization
		? `Workspace: ${activeOrganization.name}`
		: "Conta Pessoal";

	return (
		<>
			<div className="space-y-6">
				<div className="flex  items-center justify-between ">
					<div>
						<h2 className="text-2xl font-semibold">Usuários</h2>
						<div className="flex items-center gap-2 mt-1">
							<p className="text-muted-foreground">Gerencie membros da plataforma</p>
							{activeOrganization && (
								<Badge status="info" className="text-xs">
									<Building2Icon className="h-3 w-3 mr-1" />
									{workspaceContext}
								</Badge>
							)}
						</div>
					</div>
					<Button onClick={handleCreateUser}>
						<PlusIcon className="mr-2 h-4 w-4" />
						Novo Membro
					</Button>
				</div>

				{/* Filters */}
				<div className="flex flex-col sm:flex-row gap-4">
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
						<Input
							type="search"
							placeholder="Buscar por nome ou email..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-9"
						/>
					</div>

					<div className="flex gap-2">
						<Select value={roleFilter} onValueChange={setRoleFilter}>
							<SelectTrigger className="w-[150px]">
								<Filter className="h-4 w-4 mr-2" />
								<SelectValue placeholder="Função" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Funções</SelectItem>
								<SelectItem value="admin">Administradores</SelectItem>
								<SelectItem value="user">Membros</SelectItem>
							</SelectContent>
						</Select>

						{hasActiveFilters && (
							<Button variant="outline" onClick={clearFilters}>
								Limpar filtros
							</Button>
						)}
					</div>
				</div>

				{/* Stats Cards */}
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">Total de Membros</p>
									<p className="text-2xl font-bold text-foreground">{data?.total || 0}</p>
								</div>
								<div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30">
									<UsersIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
								</div>
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">Administradores</p>
									<p className="text-2xl font-bold text-foreground">
										{data?.users?.filter(u => u.role === "admin").length || 0}
									</p>
								</div>
								<div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30">
									<ShieldIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
								</div>
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">Emails Verificados</p>
									<p className="text-2xl font-bold text-foreground">
										{data?.users?.filter(u => u.emailVerified).length || 0}
									</p>
								</div>
								<div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30">
									<MailIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
								</div>
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">Aguardando Verificação</p>
									<p className="text-2xl font-bold text-foreground">
										{data?.users?.filter(u => !u.emailVerified).length || 0}
									</p>
								</div>
								<div className="p-3 rounded-full bg-orange-100 dark:bg-orange-900/30">
									<ClockIcon className="h-6 w-6 text-orange-600 dark:text-orange-400" />
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{isLoading ? (
					<div className="flex h-32 items-center justify-center">
						<Spinner className="mr-2 size-4 text-primary" />
						{t("admin.users.loading")}
					</div>
				) : paginatedUsers.length === 0 ? (
					<div className="h-24 flex flex-col items-center justify-center text-muted-foreground">
						<p>Nenhum membro encontrado.</p>
						{hasActiveFilters && (
							<Button variant="link" onClick={clearFilters} className="mt-2">
								Limpar filtros
							</Button>
						)}
					</div>
				) : (
					<>
						<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
							{paginatedUsers.map((user) => (
								<Card key={user.id} className="group hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-primary/5 to-primary/0 border-primary/20">
									<CardHeader className="pb-4">
										<div className="flex items-start justify-between">
											<div className="flex-1 min-w-0 flex items-center gap-4">
												<UserAvatar
													name={user.name ?? user.email}
													avatarUrl={user.image}
													className="h-16 w-16 rounded-full"
												/>
												<div className="min-w-0 flex-1">
													<CardTitle className="text-lg font-semibold text-foreground truncate">
														{user.name ?? user.email}
													</CardTitle>
													<p className="text-sm text-muted-foreground mt-1 truncate">
														{user.email}
													</p>
													<div className="flex gap-2 mt-2">

														{user.role === "admin" ? (
															<Badge status="success" className="text-xs flex items-center">
																<ShieldIcon className="h-3 w-3 mr-1" /> Admin
															</Badge>
														) : (
															<Badge status="info" className="text-xs flex items-center">
																<User2 className="h-3 w-3 mr-1" /> Membro
															</Badge>
														)}
													</div>
												</div>
											</div>
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button
														variant="ghost"
														size="sm"

													>
														<MoreVerticalIcon className="h-4 w-4" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuItem onClick={() => impersonateUser(user.id, { name: user.name ?? "" })}>
														<SquareUserRoundIcon className="mr-2 size-4" />
														{t("admin.users.impersonate")}
													</DropdownMenuItem>
													{!user.emailVerified && (
														<DropdownMenuItem onClick={() => resendVerificationMail(user.email)}>
															<Repeat1Icon className="mr-2 size-4" />
															{t("admin.users.resendVerificationMail.title")}
														</DropdownMenuItem>
													)}
													{user.role !== "admin" ? (
														<DropdownMenuItem onClick={() => assignAdminRole(user.id)}>
															<ShieldCheckIcon className="mr-2 size-4" />
															{t("admin.users.assignAdminRole")}
														</DropdownMenuItem>
													) : (
														<DropdownMenuItem onClick={() => removeAdminRole(user.id)}>
															<ShieldXIcon className="mr-2 size-4" />
															{t("admin.users.removeAdminRole")}
														</DropdownMenuItem>
													)}
													<DropdownMenuItem onClick={() => confirm({
														title: t("admin.users.confirmDelete.title"),
														message: t("admin.users.confirmDelete.message"),
														confirmLabel: t("admin.users.confirmDelete.confirm"),
														destructive: true,
														onConfirm: () => deleteUser(user.id),
													})}>
														<span className="flex items-center text-destructive hover:text-destructive">
															<TrashIcon className="mr-2 size-4" />
															{t("admin.users.delete")}
														</span>
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</div>
									</CardHeader>


								</Card>
							))}
						</div>

						<UsersPaginationComponent
							pagination={pagination}
							currentPage={currentPage}
							onPageChange={setCurrentPage}
						/>
					</>
				)}
			</div>

			<CreateUserModal
				isOpen={showCreateUserModal}
				onClose={() => setShowCreateUserModal(false)}
				onUserCreated={handleUserCreated}
			/>
		</>
	);
}
