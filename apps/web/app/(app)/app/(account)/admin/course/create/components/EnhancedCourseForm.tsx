"use client";

import { useState, useEffect, useCallback } from "react";
import { useR<PERSON>er, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/modules/ui/components/card";
import { <PERSON><PERSON> } from "@/modules/ui/components/button";
import { Badge } from "@/modules/ui/components/badge";
import { Loader2Icon, PackageIcon, AlertCircleIcon, CheckIcon } from "lucide-react";
import { CourseBasicForm } from "../../../courses/components/CourseBasicForm";
import { CourseStructureForm } from "../../../courses/components/CourseStructureForm";
import { CourseSettingsForm } from "../../../courses/components/CourseSettingsForm";
import { CoursePreview } from "../../../courses/components/CoursePreview";
import { CaktoProductSelector } from "@/modules/saas/admin/component/courses/CaktoProductSelector";
import type { CourseFormData } from "../../../courses/types";
import { apiClient } from "@shared/lib/api-client";

const STEPS = [
	{
		id: "basic",
		title: "Informações Básicas",
		description: "Nome, descrição e organização",
	},
	{
		id: "structure",
		title: "Estrutura e Conteúdo",
		description: "Módulos, aulas e upload de materiais",
	},
	{
		id: "settings",
		title: "Configurações",
		description: "Configurações de acesso e publicação",
	},
	{
		id: "preview",
		title: "Visualizar e Publicar",
		description: "Revise e publique seu curso",
	},
];

interface CaktoProduct {
	id: string;
	name: string;
	description?: string;
	image?: string;
	price: number;
	type: string;
	status: string;
	contentDeliveries: string[];
	alreadyLinked: boolean;
}

interface EnhancedCourseFormProps {
	mode: "create" | "edit";
	courseId?: string;
}

export default function EnhancedCourseForm({ mode, courseId }: EnhancedCourseFormProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [currentStep, setCurrentStep] = useState(0);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingProduct, setIsLoadingProduct] = useState(false);
	const [showProductSelector, setShowProductSelector] = useState(false);
	const [selectedCaktoProduct, setSelectedCaktoProduct] = useState<CaktoProduct | null>(null);

	// Get product parameter from URL
	const productParam = searchParams.get('product');
	const caktoProductId = searchParams.get('caktoProductId');
	const caktoProductName = searchParams.get('caktoProductName');

	const [formData, setFormData] = useState<CourseFormData>({
		name: "",
		description: "",
		organizationId: "",
		community: "",
		link: "",
		logo: "",
		modules: [],
	});

	// Load Cakto product data when product parameter is provided
	useEffect(() => {
		if (productParam && !selectedCaktoProduct) {
			loadCaktoProductData(productParam);
		} else if (caktoProductId && caktoProductName && !selectedCaktoProduct) {
			// Handle legacy URL format
			setSelectedCaktoProduct({
				id: caktoProductId,
				name: decodeURIComponent(caktoProductName),
				description: "",
				price: 0,
				type: "course",
				status: "active",
				contentDeliveries: ["cakto"],
				alreadyLinked: false,
			});
			setFormData(prev => ({
				...prev,
				name: decodeURIComponent(caktoProductName),
			}));
		}
	}, [productParam, caktoProductId, caktoProductName, selectedCaktoProduct]);

	const loadCaktoProductData = async (productId: string) => {
		try {
			setIsLoadingProduct(true);
			
			// Fetch product details from Cakto API
			const response = await fetch(`/api/admin/cakto-products`);
			
			if (!response.ok) {
				throw new Error("Failed to fetch Cakto products");
			}
			
			const products = await response.json();
			const product = products.find((p: CaktoProduct) => p.id === productId);
			
			if (!product) {
				throw new Error("Product not found");
			}
			
			if (product.alreadyLinked) {
				toast.error("Este produto já está associado a outro curso");
				router.push("/app/admin/courses");
				return;
			}
			
			setSelectedCaktoProduct(product);
			
			// Auto-populate form with product data
			setFormData(prev => ({
				...prev,
				name: product.name,
				description: product.description || "",
				logo: product.image || "",
			}));
			
			toast.success(`Produto "${product.name}" carregado com sucesso!`);
			
		} catch (error) {
			console.error("Error loading Cakto product:", error);
			toast.error("Erro ao carregar dados do produto da Cakto");
		} finally {
			setIsLoadingProduct(false);
		}
	};

	const updateFormData = useCallback((updates: Partial<CourseFormData>) => {
		setFormData((prev: CourseFormData) => ({ ...prev, ...updates }));
	}, []);

	const handleNext = useCallback(() => {
		if (currentStep < STEPS.length - 1) {
			setCurrentStep((prev) => prev + 1);
		}
	}, [currentStep]);

	const handlePrevious = useCallback(() => {
		if (currentStep > 0) {
			setCurrentStep((prev) => prev - 1);
		}
	}, [currentStep]);

	const handleProductSelect = (product: CaktoProduct) => {
		setSelectedCaktoProduct(product);
		setFormData(prev => ({
			...prev,
			name: product.name,
			description: product.description || prev.description,
			logo: product.image || prev.logo,
		}));
		setShowProductSelector(false);
		toast.success(`Produto "${product.name}" selecionado!`);
	};

	const handleRemoveProduct = () => {
		setSelectedCaktoProduct(null);
		// Don't clear form data as user might want to keep the populated information
		toast.info("Produto removido. Os dados do formulário foram mantidos.");
	};

	if (isLoadingProduct) {
		return (
			<div className="flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<Loader2Icon className="h-8 w-8 animate-spin mx-auto mb-4" />
					<p className="text-sm text-gray-600">Carregando dados do produto...</p>
				</div>
			</div>
		);
	}

	const renderStepContent = () => {
		switch (currentStep) {
			case 0:
				return (
					<CourseBasicForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
					/>
				);
			case 1:
				return (
					<CourseStructureForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
						onPrevious={handlePrevious}
					/>
				);
			case 2:
				return (
					<CourseSettingsForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
						onPrevious={handlePrevious}
					/>
				);
			case 3:
				return (
					<CoursePreview
						data={formData}
						onPrevious={handlePrevious}
					/>
				);
			default:
				return null;
		}
	};

	return (
		<div className="container mx-auto py-8 px-4">
			<div className="max-w-4xl mx-auto">
				{/* Header */}
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						{mode === "create" ? "Criar Novo Curso" : "Editar Curso"}
					</h1>
					<p className="text-gray-600">
						{mode === "create" 
							? "Configure seu curso seguindo os passos abaixo"
							: "Edite as informações do seu curso"
						}
					</p>
				</div>

				{/* Cakto Product Info */}
				{selectedCaktoProduct && (
					<Card className="mb-6 border-blue-200 bg-blue-50/50">
						<CardHeader className="pb-3">
							<CardTitle className="flex items-center gap-2 text-lg">
								<PackageIcon className="h-5 w-5 text-blue-600" />
								Produto Cakto Associado
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="flex items-start justify-between">
								<div className="flex-1">
									<h3 className="font-semibold text-gray-900">{selectedCaktoProduct.name}</h3>
									{selectedCaktoProduct.description && (
										<p className="text-sm text-gray-600 mt-1">{selectedCaktoProduct.description}</p>
									)}
									<div className="flex items-center gap-2 mt-2">
										<Badge variant="secondary" className="bg-blue-100 text-blue-800">
											{selectedCaktoProduct.type}
										</Badge>
										<Badge variant="outline" className="text-green-700 border-green-300">
											<CheckIcon className="h-3 w-3 mr-1" />
											Ativo
										</Badge>
									</div>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={handleRemoveProduct}
									className="text-red-600 hover:text-red-700 hover:bg-red-50"
								>
									Remover
								</Button>
							</div>
						</CardContent>
					</Card>
				)}

				{/* Product Selection Button */}
				{!selectedCaktoProduct && (
					<Card className="mb-6 border-dashed border-gray-300">
						<CardContent className="py-8">
							<div className="text-center">
								<PackageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
								<h3 className="text-lg font-semibold text-gray-900 mb-2">
									Associar Produto Cakto (Opcional)
								</h3>
								<p className="text-gray-600 mb-4">
									Selecione um produto da Cakto para pré-popular os dados do curso
								</p>
								<Button
									onClick={() => setShowProductSelector(true)}
									variant="outline"
									className="border-blue-300 text-blue-700 hover:bg-blue-50"
								>
									<PackageIcon className="h-4 w-4 mr-2" />
									Selecionar Produto
								</Button>
							</div>
						</CardContent>
					</Card>
				)}

				{/* Step Content */}
				{renderStepContent()}

				{/* Product Selector Modal */}
				<CaktoProductSelector
					open={showProductSelector}
					onOpenChange={setShowProductSelector}
					onProductSelect={handleProductSelect}
				/>
			</div>
		</div>
	);
}
