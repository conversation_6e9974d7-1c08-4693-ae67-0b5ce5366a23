# Documentação do Webhook de Compras Cakto

## Visão Geral

O endpoint `/api/webhooks/cakto/purchase` é responsável por processar webhooks de compras aprovadas da plataforma Cakto e automaticamente:

1. **Criar conta do aluno** (se não existir)
2. **Liberar acesso ao curso** associado ao produto comprado
3. **Enviar magic link** para acesso direto ao curso

## Endpoint

```
POST https://aluno.cakto.com.br/api/webhooks/cakto/purchase
```

## Autenticação

O webhook utiliza autenticação HMAC SHA-256 para garantir segurança:

- **Header**: `X-Cakto-Signature`
- **Secret**: Configurado via `MEMBERS_WEBHOOK_SECRET`
- **Algoritmo**: HMAC SHA-256 do corpo da requisição

## Fluxo de Funcionamento

### 1. Validação de Segurança
- Verifica se `MEMBERS_WEBHOOK_SECRET` está configurado
- Valida assinatura HMAC se presente no header
- Valida estrutura do payload usando schema Zod

### 2. Filtros de Evento
- **Evento aceito**: `purchase_approved`
- **Status aceitos**: `approved` ou `completed`
- Outros eventos/status são ignorados (retorna 200 OK)

### 3. Criação/Verificação do Usuário
- Busca usuário por email (`customer.email`)
- Se não existir, cria novo usuário:
  - Email verificado automaticamente
  - Sem senha (usa magic link)
  - Role: "user"
  - Onboarding incompleto

### 4. Associação à Organização
- Busca/cria organização "Cakto Members"
- Adiciona usuário como membro se necessário
- Role na organização: "member"

### 5. Liberação de Acesso ao Curso
- Busca curso associado ao produto via `CourseProduct.caktoProductId`
- Verifica se usuário já tem acesso
- Cria registro em `UserCourses` sem data de expiração

### 6. Envio de Magic Link
- Gera magic link usando Better Auth
- Callback URL: `/app/courses/{courseId}`
- Enviado para email do cliente

## Payload do Webhook

### Estrutura Esperada

```json
{
  "secret": "webhook_secret",
  "event": "purchase_approved",
  "data": {
    "id": "purchase_id",
    "refId": "reference_id",
    "customer": {
      "name": "Nome do Cliente",
      "email": "<EMAIL>",
      "phone": "+5511999999999",
      "docNumber": "12345678901"
    },
    "product": {
      "id": "product_id_cakto",
      "name": "Nome do Produto",
      "type": "course"
    },
    "status": "approved",
    "amount": 99.90,
    "paymentMethod": "credit_card",
    "paidAt": "2024-01-15T10:30:00Z",
    "createdAt": "2024-01-15T10:00:00Z"
  }
}
```

### Campos Obrigatórios

- `event`: Deve ser "purchase_approved"
- `data.customer.name`: Nome do cliente
- `data.customer.email`: Email válido do cliente
- `data.product.id`: ID do produto na Cakto
- `data.status`: "approved" ou "completed"

## Respostas da API

### Sucesso (200)
```json
{
  "success": true,
  "message": "Purchase processed successfully",
  "data": {
    "userId": "user_id",
    "courseId": "course_id",
    "courseName": "Nome do Curso"
  }
}
```

### Usuário já tem acesso (200)
```json
{
  "success": true,
  "message": "User already has access to this course"
}
```

### Evento ignorado (200)
```json
{
  "success": true,
  "message": "Webhook received but event is not purchase_approved (event_name)"
}
```

### Produto não encontrado (404)
```json
{
  "success": false,
  "message": "No course found for product ID: product_id"
}
```

### Erro de autenticação (401)
```json
{
  "success": false,
  "message": "Invalid signature"
}
```

### Payload inválido (400)
```json
{
  "success": false,
  "message": "Invalid payload"
}
```

### Erro interno (500)
```json
{
  "success": false,
  "message": "Error processing webhook: error_details"
}
```

## Pré-requisitos

### Variáveis de Ambiente
```bash
# Obrigatório - Secret para validação HMAC
MEMBERS_WEBHOOK_SECRET=seu_secret_aqui

# Obrigatório - URL base da aplicação
NEXT_PUBLIC_APP_URL=https://members.cakto.com.br

# Obrigatório - URL do banco de dados
DATABASE_URL=postgresql://...
```

### Associação Produto-Curso

Antes de receber webhooks, é necessário associar produtos Cakto aos cursos:

1. Acesse o painel admin
2. Vá em "Cursos" > "Produtos Cakto"
3. Associe cada produto ao curso correspondente
4. Isso cria registro na tabela `CourseProduct`

## Como Testar

### 1. Teste Manual com cURL

```bash
# Gerar assinatura HMAC
echo -n '{"secret":"test","event":"purchase_approved","data":{"customer":{"name":"Teste","email":"<EMAIL>"},"product":{"id":"product_id"},"status":"approved"}}' | openssl dgst -sha256 -hmac "seu_secret"

# Fazer requisição
curl -X POST https://aluno.cakto.com.br/api/webhooks/cakto/purchase \
  -H "Content-Type: application/json" \
  -H "X-Cakto-Signature: assinatura_gerada" \
  -d '{
    "secret": "test",
    "event": "purchase_approved",
    "data": {
      "id": "test_purchase_123",
      "refId": "ref_123",
      "customer": {
        "name": "João Silva",
        "email": "<EMAIL>"
      },
      "product": {
        "id": "product_id_existente",
        "name": "Curso Teste"
      },
      "status": "approved",
      "createdAt": "2024-01-15T10:00:00Z"
    }
  }'
```

### 2. Verificar Logs

Os logs são registrados com as seguintes informações:
- Criação de usuários
- Adição à organização
- Concessão de acesso ao curso
- Envio de magic link
- Erros e validações

### 3. Validar no Banco

```sql
-- Verificar usuário criado
SELECT * FROM "user" WHERE email = '<EMAIL>';

-- Verificar membro da organização
SELECT m.*, o.name as org_name 
FROM member m 
JOIN organization o ON m."organizationId" = o.id 
WHERE m."userId" = 'user_id';

-- Verificar acesso ao curso
SELECT uc.*, c.name as course_name 
FROM "userCourses" uc 
JOIN courses c ON uc."courseId" = c.id 
WHERE uc."userId" = 'user_id';
```

## Troubleshooting

### Produto não encontrado
- Verificar se existe registro em `CourseProduct` com o `caktoProductId`
- Associar produto ao curso no painel admin

### Assinatura inválida
- Verificar se `MEMBERS_WEBHOOK_SECRET` está correto
- Validar geração da assinatura HMAC

### Magic link não enviado
- Verificar configuração do Better Auth
- Verificar logs de erro no envio de email

### Usuário não criado
- Verificar se email é válido
- Verificar logs de erro na criação

## Monitoramento

O webhook registra logs detalhados para monitoramento:

- ✅ Webhooks processados com sucesso
- ⚠️ Webhooks ignorados (evento/status incorreto)
- ❌ Erros de validação ou processamento
- 📧 Status do envio de magic links
- 👤 Criação de novos usuários
- 🎓 Concessão de acesso a cursos

## Segurança

- **HMAC SHA-256**: Validação de integridade
- **Validação de schema**: Zod para estrutura do payload
- **Rate limiting**: Implementado no nível do servidor
- **Logs de auditoria**: Todos os eventos são registrados
- **Sem exposição de dados**: Senhas não são armazenadas

---

**Desenvolvido para**: Cakto Members v2  
**Última atualização**: Janeiro 2024  
**Contato**: Equipe de desenvolvimento Cakto